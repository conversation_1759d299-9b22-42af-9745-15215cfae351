import React from 'react'
import './App.css'
import {BrowserRouter,Route,Routes} from 'react-router-dom'
import 'bootstrap/dist/css/bootstrap.min.css'
import CreateUser from './CreateUser'
import UpdateUser from './UpdateUser'
import User from './User'

function App() {
  return (
    <>
      <BrowserRouter>
      <Routes>
        <Route path='/' element={<User/>}/>
        <Route path='/create' element={<CreateUser/>} />
        <Route path='/update/:id' element={<UpdateUser/>} />
      </Routes>
      </BrowserRouter>
    </>
  )
}

export default App
