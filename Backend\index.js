import express from 'express'
import dotenv from 'dotenv'
import cors from 'cors'
import mongoose from 'mongoose'
import UserRoute from './routers/UserRoute.js'

dotenv.config()

const app=express()

app.use(express.json())
app.use(cors())
app.use(express.urlencoded({extended:true}))
app.use('/uploads', express.static('uploads'))

const PORT=process.env.PORT || 4000

mongoose.connect(process.env.MONGODB,{}).then(()=>{
    console.log("connected to database")
})

app.use('/api',UserRoute)

app.listen(PORT,()=>{
    console.log(`server is running on port ${PORT}`)
})
