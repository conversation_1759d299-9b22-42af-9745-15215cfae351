import React from 'react'
import axios from 'axios'
import { useState } from 'react'

function CreateUser() {
    const [name, setName] = useState()
    const [email, setEmail] = useState()
    const [age, setAge] = useState()

    const SubmitHandler=(e)=>{
        e.preventDefault()
        axios.post('http://localhost:4000/api/create',{name,email,age}).then((data)=>{
            console.log(data)
        })
        .catch((err)=>{
                console.log(err)
        })
    }

  return (
    <div className='d-flex vh-100 bg-primary justify-content-center align-items-center'>
        <div className='w-50 bg-white p-3 rounded'>
            <form action="" onSubmit={SubmitHandler}>
                <h2>Add User</h2>
                <div className='mb-2'>
                    <label >Name</label>
                    <input type="text" name="" value={name} 
                    onChange={(e)=>setName(e.target.value)}
                     placeholder='Enter Name' className='form-control' />
                </div>

                
                <div className='mb-2'>
                    <label >Email</label>
                    <input type="Email" name="" value={email} 
                    onChange={(e)=>setEmail(e.target.value)} 
                    placeholder='Enter Email' className='form-control' />
                </div>

                
                <div className='mb-2'>
                    <label >Age</label>
                    <input type="text" name="" value={age}
                    onChange={(e)=>{
                        setAge(e.target.value)
                    }} 
                    placeholder='Enter Age' className='form-control' />
                </div>

                
                
                <button className='btn btn-success'>Submit</button>
            </form>
        </div>

    </div>
  )
}

export default CreateUser