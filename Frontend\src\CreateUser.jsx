import React from 'react'
import axios from 'axios'
import { useState } from 'react'
import {useNavigate, Link} from 'react-router-dom'

function CreateUser() {
    const [name, setName] = useState('')
    const [email, setEmail] = useState('')
    const [password,setPassword] = useState('')
    const [age, setAge] = useState('')
    const [image, setImage] = useState(null)
    const [imagePreview, setImagePreview] = useState(null)
    const navigate = useNavigate()

    const handleImageChange = (e) => {
        const file = e.target.files[0]
        if (file) {
            setImage(file)
            const reader = new FileReader()
            reader.onloadend = () => {
                setImagePreview(reader.result)
            }
            reader.readAsDataURL(file)
        }
    }

    const SubmitHandler=(e)=>{
        e.preventDefault()
        const formData = new FormData()
        formData.append('name', name)
        formData.append('email', email)
        formData.append('password',password)
        formData.append('age', age)
        if (image) {
            formData.append('image', image)
        }

        axios.post('http://localhost:4000/api/create', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        }).then((data)=>{
            console.log(data)
            navigate('/')
        })
        .catch((err)=>{
                console.log(err)
        })
    }

  return (
    <div className='d-flex vh-100 bg-primary justify-content-center align-items-center'>
        <div className='w-50 bg-white p-3 rounded'>
            <form action="" onSubmit={SubmitHandler}>
                <div className='d-flex justify-content-between align-items-center mb-3'>
                    <h2>Add User</h2>
                    <Link to='/' className='btn btn-outline-secondary btn-sm'>Back to Users</Link>
                </div>
                <div className='mb-2'>
                    <label >Name</label>
                    <input type="text" name="" value={name} 
                    onChange={(e)=>setName(e.target.value)}
                     placeholder='Enter Name' className='form-control' />
                </div>

                
                <div className='mb-2'>
                    <label >Email</label>
                    <input type="Email" name="" value={email} 
                    onChange={(e)=>setEmail(e.target.value)} 
                    placeholder='Enter Email' className='form-control' />
                </div>

                
                <div className='mb-2'>
                    <label >Password</label>
                    <input type="password" name="" value={password} 
                    onChange={(e)=>setPassword(e.target.value)} 
                    placeholder='Enter Password' className='form-control' />
                </div>
                
                <div className='mb-2'>
                    <label >Age</label>
                    <input type="text" name="" value={age}
                    onChange={(e)=>{
                        setAge(e.target.value)
                    }}
                    placeholder='Enter Age' className='form-control' />
                </div>

                <div className='mb-2'>
                    <label>Profile Image</label>
                    <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageChange}
                        className='form-control'
                    />
                    {imagePreview && (
                        <div className='mt-2'>
                            <img
                                src={imagePreview}
                                alt="Preview"
                                style={{width: '100px', height: '100px', objectFit: 'cover'}}
                            />
                        </div>
                    )}
                </div>

                <button className='btn btn-success'>Submit</button>
            </form>
        </div>

    </div>
  )
}

export default CreateUser