import mongoose from 'mongoose'
import { type } from 'os'

const userSchema =new mongoose.Schema({
    name:{
        type:String,
        required:true
    },
    email:{
        type:String,
        required:true
    },
    password:{
        type:String
    },
    age:{
        type:String,
        required:true
    },
    image:{
        type:String,
        default:''
    },
})

const User=mongoose.model('user',userSchema)

export default User