import React, { useState } from 'react'
import {useNavigate, useParams} from 'react-router-dom'
import {useEffect} from 'react'
import axios from 'axios'

function UpdateUser() {
    const [name, setName] = useState()
    const [email, setEmail] = useState()

    const [age, setAge] = useState()
    const navigate=useNavigate()
    useEffect(() => {
      axios.put('http://localhost:4000/api/update/${id}',{name,email,age}).then((data)=>{
        console.log(data)
        navigate('/')
      })
      .catch((err)=>{
        console.log(err)
      })
    }, [])
    


  return (
   <div className='d-flex vh-100 bg-primary justify-content-center align-items-center'>
        <div className='w-50 bg-white p-3 rounded'>
            <form action="">
                <h2>Update User</h2>
                <div className='mb-2'>
                    <label >Name</label>
                    <input type="text" name="" placeholder='Enter Name' className='form-control' />
                </div>

                
                <div className='mb-2'>
                    <label >Email</label>
                    <input type="Email" name="" placeholder='Enter Email' className='form-control' />
                </div>

                
                <div className='mb-2'>
                    <label >Age</label>
                    <input type="text" name="" placeholder='Enter Age' className='form-control' />
                </div>

                
                <button className='btn btn-success'>Update</button>
            </form>
        </div>

    </div>
  )
}

export default UpdateUser