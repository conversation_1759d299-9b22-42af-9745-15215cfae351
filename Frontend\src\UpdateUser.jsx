import React, { useState } from 'react'
import {useNavigate, usePara<PERSON>, <PERSON>} from 'react-router-dom'
import {useEffect} from 'react'
import axios from 'axios'

function UpdateUser() {
    const [name, setName] = useState('')
    const [email, setEmail] = useState('')
    const [age, setAge] = useState('')
    const [image, setImage] = useState(null)
    const [currentImage, setCurrentImage] = useState('')
    const [imagePreview, setImagePreview] = useState(null)
    const navigate = useNavigate()
    const {id} = useParams()

    // Handle image change
    const handleImageChange = (e) => {
        const file = e.target.files[0]
        if (file) {
            setImage(file)
            const reader = new FileReader()
            reader.onloadend = () => {
                setImagePreview(reader.result)
            }
            reader.readAsDataURL(file)
        }
    }

    // Load user data when component mounts
    useEffect(() => {
      axios.get(`http://localhost:4000/api/user/${id}`).then((response)=>{
        setName(response.data.name)
        setEmail(response.data.email)
        setAge(response.data.age)
        setCurrentImage(response.data.image || '')
      })
      .catch((err)=>{
        console.log(err)
      })
    }, [id])

    // Handle form submission
    const handleSubmit = (e) => {
      e.preventDefault()
      const formData = new FormData()
      formData.append('name', name)
      formData.append('email', email)
      formData.append('age', age)
      if (image) {
          formData.append('image', image)
      }

      axios.put(`http://localhost:4000/api/update/${id}`, formData, {
          headers: {
              'Content-Type': 'multipart/form-data'
          }
      }).then((data)=>{
        console.log(data)
        navigate('/')
      })
      .catch((err)=>{
        console.log(err)
      })
    }
    


  return (
   <div className='d-flex vh-100 bg-primary justify-content-center align-items-center'>
        <div className='w-50 bg-white p-3 rounded'>
            <form onSubmit={handleSubmit}>
                <div className='d-flex justify-content-between align-items-center mb-3'>
                    <h2>Update User</h2>
                    <Link to='/' className='btn btn-outline-secondary btn-sm'>Back to Users</Link>
                </div>
                <div className='mb-2'>
                    <label>Name</label>
                    <input
                        type="text"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        placeholder='Enter Name'
                        className='form-control'
                    />
                </div>

                <div className='mb-2'>
                    <label>Email</label>
                    <input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder='Enter Email'
                        className='form-control'
                    />
                </div>

                <div className='mb-2'>
                    <label>Age</label>
                    <input
                        type="text"
                        value={age}
                        onChange={(e) => setAge(e.target.value)}
                        placeholder='Enter Age'
                        className='form-control'
                    />
                </div>

                <div className='mb-2'>
                    <label>Profile Image</label>
                    <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageChange}
                        className='form-control'
                    />
                    {currentImage && !imagePreview && (
                        <div className='mt-2'>
                            <p>Current Image:</p>
                            <img
                                src={`http://localhost:4000/uploads/${currentImage}`}
                                alt="Current"
                                style={{width: '100px', height: '100px', objectFit: 'cover'}}
                            />
                        </div>
                    )}
                    {imagePreview && (
                        <div className='mt-2'>
                            <p>New Image Preview:</p>
                            <img
                                src={imagePreview}
                                alt="Preview"
                                style={{width: '100px', height: '100px', objectFit: 'cover'}}
                            />
                        </div>
                    )}
                </div>

                <button type="submit" className='btn btn-success'>Update</button>
            </form>
        </div>

    </div>
  )
}

export default UpdateUser