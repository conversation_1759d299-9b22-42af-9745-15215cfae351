import React, { useState } from 'react'
import axios from 'axios'
import { useNavigate, Link } from 'react-router-dom'

function Login() {
    const [email, setEmail] = useState('')
    const [password, setPassword] = useState('')
    const [error, setError] = useState('')
    const [loading, setLoading] = useState(false)
    const navigate = useNavigate()

    const handleSubmit = (e) => {
        e.preventDefault()
        setError('')
        setLoading(true)

        axios.post('http://localhost:4000/api/login', { email, password })
            .then((response) => {
                if (response.data.success) {
                    // Store user data in localStorage (simple approach)
                    localStorage.setItem('user', JSON.stringify(response.data.user))
                    localStorage.setItem('isLoggedIn', 'true')
                    
                    // Navigate to users list
                    navigate('/')
                } else {
                    setError(response.data.message)
                }
            })
            .catch((err) => {
                if (err.response) {
                    setError(err.response.data.message)
                } else {
                    setError('Network error. Please try again.')
                }
            })
            .finally(() => {
                setLoading(false)
            })
    }

    return (
        <div className='d-flex vh-100 bg-primary justify-content-center align-items-center'>
            <div className='w-50 bg-white p-4 rounded'>
                <form onSubmit={handleSubmit}>
                    <h2 className='mb-4 text-center'>Login</h2>
                    
                    {error && (
                        <div className='alert alert-danger' role='alert'>
                            {error}
                        </div>
                    )}

                    <div className='mb-3'>
                        <label className='form-label'>Email</label>
                        <input 
                            type="email" 
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            placeholder='Enter your email' 
                            className='form-control'
                            required
                        />
                    </div>

                    <div className='mb-3'>
                        <label className='form-label'>Password</label>
                        <input 
                            type="password" 
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            placeholder='Enter your password (use your name)' 
                            className='form-control'
                            required
                        />
                        <small className='form-text text-muted'>
                            For demo: use your registered name as password
                        </small>
                    </div>

                    <button 
                        type="submit" 
                        className='btn btn-success w-100 mb-3'
                        disabled={loading}
                    >
                        {loading ? 'Logging in...' : 'Login'}
                    </button>

                    <div className='text-center'>
                        <p>Don't have an account? <Link to='/create'>Create Account</Link></p>
                        <p>View all users: <Link to='/users'>Users List</Link></p>
                    </div>
                </form>
            </div>
        </div>
    )
}

export default Login
