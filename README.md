# CRUD Operations and User Management System

A full-stack MERN application for user management with image upload functionality.

## Features

- ✅ **Create User** - Add new users with profile images
- ✅ **Read Users** - Display all users in a table with images
- ✅ **Update User** - Edit user details and update profile images
- ✅ **Delete User** - Remove users from the database
- ✅ **Image Upload** - Profile picture upload with preview
- ✅ **Responsive Design** - Bootstrap-styled interface

## Tech Stack

### Frontend
- **React.js** - User interface
- **React Router** - Navigation
- **Axios** - HTTP requests
- **Bootstrap** - Styling

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MongoDB** - Database
- **Mongoose** - ODM
- **Multer** - File upload handling

## Project Structure

```
├── Backend/
│   ├── models/
│   │   └── User.js          # User schema
│   ├── routers/
│   │   └── UserRoute.js     # API routes
│   ├── uploads/             # Uploaded images
│   ├── index.js             # Server entry point
│   └── package.json
├── Frontend/
│   ├── src/
│   │   ├── CreateUser.jsx   # Create user form
│   │   ├── UpdateUser.jsx   # Update user form
│   │   ├── User.jsx         # Users list
│   │   ├── App.jsx          # Main app component
│   │   └── main.jsx         # Entry point
│   └── package.json
└── README.md
```

## Installation & Setup

### Prerequisites
- Node.js (v14 or higher)
- MongoDB
- Git

### Backend Setup
```bash
cd Backend
npm install
npm run dev
```

### Frontend Setup
```bash
cd Frontend
npm install
npm run dev
```

### Environment Variables
Create a `.env` file in the Backend directory:
```
MONGODB=mongodb://localhost:27017/your-database-name
PORT=4000
```

## API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/create` | Create new user |
| GET | `/api/users` | Get all users |
| GET | `/api/user/:id` | Get user by ID |
| PUT | `/api/update/:id` | Update user |
| DELETE | `/api/delete/:id` | Delete user |

## Usage

1. **Start the backend server** (runs on port 4000)
2. **Start the frontend development server** (runs on port 5173)
3. **Navigate to** `http://localhost:5173`
4. **Create, view, edit, and delete users** with the interface

## Features in Detail

### Image Upload
- Supports common image formats (JPG, PNG, GIF)
- 5MB file size limit
- Image preview before upload
- Circular profile picture display

### User Management
- Form validation
- Error handling
- Responsive design
- Clean navigation

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is open source and available under the [MIT License](LICENSE).
