import React, {useEffect, useState} from 'react'
import { <PERSON> } from 'react-router-dom'
import axios from 'axios'

function User() {
    // Change users to be an array of objects instead of a single object
    const [users, setUsers] = useState([
        {Name: "yourself", Email: "<EMAIL>", Age: 20}
    ])
    
    useEffect(() => {
      axios.get('http://localhost:4000/api/user').then((data)=>{
        setUsers(data.data)
      })
      .catch((err)=>{
        console.log(err)
      })
    }, [])
    
  return (
    <div className='d-flex vh-100 bg-primary justify-content-center align-items-center'>
        <div className='bg-white w-50 rounded p-3'>
            <Link to='/create' className='btn btn-success btn-sm mb-3'>ADD + </Link>
            <table className='table'>
                <thead>
                   <tr>
                     <th>Name</th>
                    <th>Email</th>
                    <th>Age</th>
                    <th>Action</th>
                   </tr>
                </thead>
                <tbody>
                    {
                        users.map((user)=>{
                            return <tr>
                                <td>{user.Name}</td>
                                <td>{user.Email}</td>
                                <td>{user.Age}</td>
                                <td>
                                 <Link to='/update' className='btn btn-success '>Edit </Link>
                                 <Link to='/delete' className='btn btn-danger '>Delete </Link>
                                 </td>


                            </tr>
                        })
                    }
                </tbody>
            </table>
        </div>
    </div>
  )
}

export default User
