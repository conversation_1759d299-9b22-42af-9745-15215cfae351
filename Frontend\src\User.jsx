import React, {useEffect, useState} from 'react'
import { <PERSON> } from 'react-router-dom'
import axios from 'axios'

function User() {
    // Initialize with empty array, data will be loaded from API
    const [users, setUsers] = useState([])
    
    useEffect(() => {
      axios.get('http://localhost:4000/api/user').then((data)=>{
        setUsers(data.data)
      })
      .catch((err)=>{
        console.log(err)
      })
    }, [])

    const DeleteHandler=(id)=>{
            axios.delete(`http://localhost:4000/api/delete/${id}`).then((data)=>{
                setUsers(users.filter((user)=>user._id !== id))
            })
    }
    
  return (
    <div className='d-flex vh-100 bg-primary justify-content-center align-items-center'>
        <div className='bg-white w-70 rounded p-3'>
            <Link to='/create' className='btn btn-success btn-sm mb-3'>ADD + </Link>
            <table className='table'>
                <thead>
                   <tr>
                     <th>Name</th>
                    <th>Email</th>
                    <th>Age</th>
                    <th>Action</th>
                   </tr>
                </thead>
                <tbody>
                    {
                        users.map((user, index)=>{
                            return <tr key={user._id || index}>
                                <td>{user.name}</td>
                                <td>{user.email}</td>
                                <td>{user.age}</td>
                                <td >
                                 <Link to={`/update/${user._id}`} className='btn btn-success  '>Edit </Link>
                                 <button className='btn btn-danger' onClick={(e)=>{
                                    DeleteHandler(user._id)
                                 }}>Delete</button>
                                 </td>
                            </tr>
                        })
                    }
                </tbody>
            </table>
        </div>
    </div>
  )
}

export default User
