import express from 'express'
import UserModel from '../models/User.js'
import multer from 'multer'
import path from 'path'
import fs from 'fs'

// Create uploads directory if it doesn't exist
const uploadsDir = 'uploads'
if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true })
}

const storage=multer.diskStorage({
    destination:(req,file,cb)=>{
        cb(null,'uploads/')
    },
    filename:(req,file,cb)=>{
        cb(null,Date.now()+path.extname(file.originalname))
    }
})

const upload = multer({
    storage: storage,
    fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true)
        } else {
            cb(new Error('Only image files are allowed!'), false)
        }
    },
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
    }
})

const router=express()


router.post('/create', upload.single('image'), (req,res)=>{
    const userData = {
        name: req.body.name,
        email: req.body.email,
        age: req.body.age,
        image: req.file ? req.file.filename : ''
    }

    UserModel.create(userData).then((data)=>{
        res.json(data)
    })
    .catch((err)=>{
        res.status(400).json(err)
    })
})

router.get('/users',(req,res)=>{
    UserModel.find({}).then((data)=>{
        res.json(data)
    })
    .catch((err)=>{
        res.status(400).json(err)
    })
})

router.get('/user/:id',(req,res)=>{
    UserModel.findById(req.params.id).then((data)=>{
        res.json(data)
    })
    .catch((err)=>{
        res.status(400).json(err)
    })
})

router.put('/update/:id', upload.single('image'), (req,res)=>{
    const updateData = {
        name: req.body.name,
        email: req.body.email,
        age: req.body.age
    }

    // Only update image if a new one is uploaded
    if (req.file) {
        updateData.image = req.file.filename
    }

    UserModel.findByIdAndUpdate(req.params.id, updateData, {new:true}).then((data)=>{
        res.json(data)
    })
    .catch((Err)=>{
        res.status(400).json(Err)
    })
})

router.delete('/delete/:id',(req,res)=>{
    UserModel.findByIdAndDelete(req.params.id).then((data)=>{
        res.json(data)
    })
    .catch((err)=>{
        res.json(err)
    })
})

// Simple login route
router.post('/login', (req, res) => {
    const { email, password } = req.body

    // Simple validation - in real app, you'd hash passwords
    UserModel.findOne({ email: email }).then((user) => {
        if (user) {
            // For simplicity, we'll use the user's name as password
            // In real app, you'd compare hashed passwords
            if (password === user.name) {
                res.json({
                    success: true,
                    message: 'Login successful',
                    user: {
                        id: user._id,
                        name: user.name,
                        email: user.email,
                        age: user.age,
                        image: user.image
                    }
                })
            } else {
                res.status(401).json({
                    success: false,
                    message: 'Invalid password'
                })
            }
        } else {
            res.status(404).json({
                success: false,
                message: 'User not found'
            })
        }
    })
    .catch((err) => {
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: err
        })
    })
})

export default router