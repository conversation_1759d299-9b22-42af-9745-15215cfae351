import express from 'express'
import UserModel from '../models/User.js'

const router=express()

router.post('/create',(req,res)=>{
    UserModel.create(req.body).then((data)=>{
        res.json(data)
    })
    .catch((err)=>{
        res.status(400).json(err)
    })
})

router.get('/user',(req,res)=>{
    UserModel.find({}).then((data)=>{
        res.json(data)
    })
    .catch((err)=>{
        res.status(400).json(err)
    })
})

router.put('/update/:id',(req,res)=>{
    UserModel.findByIdAndUpdate(req.params.id,req.body,{new:true}).then((data)=>{
        res.json(data)
    })
    .catch((Err)=>{
        res.status(400).json(Err)
    })
})

export default router