import mongoose from 'mongoose'
import dotenv from 'dotenv'
import UserModel from './models/User.js'

dotenv.config()

// Connect to MongoDB
mongoose.connect(process.env.MONGODB, {}).then(() => {
    console.log("Connected to database")
    seedUsers()
}).catch((err) => {
    console.log("Database connection error:", err)
})

// Dummy users data
const dummyUsers = [
    {
        name: "<PERSON>",
        email: "<EMAIL>",
        age: "25",
        image: ""
    },
    {
        name: "<PERSON>",
        email: "<EMAIL>", 
        age: "28",
        image: ""
    },
    {
        name: "<PERSON>",
        email: "<EMAIL>",
        age: "30",
        image: ""
    },
    {
        name: "<PERSON>",
        email: "<EMAIL>",
        age: "22",
        image: ""
    },
    {
        name: "<PERSON>",
        email: "<EMAIL>",
        age: "35",
        image: ""
    }
]

async function seedUsers() {
    try {
        // Clear existing users (optional)
        // await UserModel.deleteMany({})
        // console.log("Cleared existing users")

        // Check if users already exist
        const existingUsers = await UserModel.find({})
        if (existingUsers.length > 0) {
            console.log("Users already exist in database:")
            existingUsers.forEach(user => {
                console.log(`- Email: ${user.email}, Password: ${user.name}`)
            })
        } else {
            // Add dummy users
            const createdUsers = await UserModel.insertMany(dummyUsers)
            console.log("Dummy users created successfully!")
            console.log("\n=== LOGIN CREDENTIALS ===")
            createdUsers.forEach(user => {
                console.log(`Email: ${user.email} | Password: ${user.name}`)
            })
        }
        
        console.log("\n=== HOW TO LOGIN ===")
        console.log("1. Go to http://localhost:5173/login")
        console.log("2. Use any email from above")
        console.log("3. Use the corresponding name as password")
        console.log("Example: Email: <EMAIL>, Password: John")
        
        process.exit(0)
    } catch (error) {
        console.error("Error seeding users:", error)
        process.exit(1)
    }
}
